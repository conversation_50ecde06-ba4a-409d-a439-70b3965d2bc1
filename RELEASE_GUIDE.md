# 自动化发布流程指南

## 概述

本项目已配置完全自动化的发布流程，基于语义版本管理（Semantic Versioning）和约定式提交（Conventional Commits）。

## 工作流程

### 1. 开发阶段
- 在 `main` 分支进行日常开发
- 使用约定式提交格式编写提交信息

### 2. 发布准备
- 将要发布的更改合并到 `release` 分支
- 推送到 `release` 分支会自动触发发布流程

### 3. 自动化流程
1. **语义分析**：分析提交历史，确定版本号增量
2. **版本更新**：自动更新 `extension.toml` 和 `package.json` 中的版本号
3. **生成变更日志**：更新 `CHANGELOG.md`
4. **创建标签**：创建新的 git tag（格式：`v0.0.0`）
5. **发布插件**：自动发布到 `chenmijiang/zed-extensions`

## 约定式提交格式

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

### 提交类型

| 类型 | 说明 | 版本影响 |
|------|------|----------|
| `feat` | 新功能 | minor |
| `fix` | 修复bug | patch |
| `perf` | 性能优化 | patch |
| `refactor` | 重构 | patch |
| `revert` | 回滚 | patch |
| `docs` | 文档更新 | 无 |
| `style` | 代码格式 | 无 |
| `test` | 测试相关 | 无 |
| `chore` | 构建/工具 | 无 |
| `ci` | CI配置 | 无 |

### 示例提交

```bash
# 新功能（minor版本）
feat: add cyberpunk theme variant

# 修复bug（patch版本）
fix: correct color contrast in light theme

# 性能优化（patch版本）
perf: optimize theme loading performance

# 重大变更（major版本）
feat!: redesign theme structure

# 或者在footer中说明
feat: redesign theme structure

BREAKING CHANGE: theme file structure has been completely redesigned
```

## 使用步骤

### 1. 初始设置

```bash
# 安装依赖
npm install

# 创建release分支（如果不存在）
git checkout -b release
git push -u origin release
```

### 2. 日常开发

```bash
# 在main分支开发
git checkout main

# 使用约定式提交
git commit -m "feat: add new theme color scheme"
git commit -m "fix: resolve theme loading issue"
```

### 3. 发布新版本

```bash
# 合并到release分支
git checkout release
git merge main

# 推送触发自动发布
git push origin release
```

## 环境变量配置

确保在 GitHub 仓库设置中配置以下 secrets：

- `GITHUB_TOKEN`：GitHub 自动提供，用于创建 release 和 tag
- `COMMITTER_TOKEN`：用于推送到 `chenmijiang/zed-extensions` 的个人访问令牌

## 版本号规则

- **Major (1.0.0)**：包含 BREAKING CHANGE 的提交
- **Minor (0.1.0)**：feat 类型的提交
- **Patch (0.0.1)**：fix、perf、refactor、revert 类型的提交

## 故障排除

### 发布失败
1. 检查提交信息格式是否符合约定式提交规范
2. 确认 `COMMITTER_TOKEN` 权限正确
3. 查看 GitHub Actions 日志获取详细错误信息

### 版本号不正确
1. 检查提交类型是否正确
2. 确认是否需要 BREAKING CHANGE 标记
3. 手动触发工作流重新运行

### 回滚版本
```bash
# 回滚到指定版本
git checkout release
git reset --hard <commit-hash>
git push --force-with-lease origin release
```

## 监控和维护

- 定期检查 GitHub Actions 工作流状态
- 监控 `chenmijiang/zed-extensions` 仓库的更新
- 保持依赖项更新（semantic-release 相关包）
