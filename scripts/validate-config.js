#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🔍 验证项目配置...\n');

// 检查必要文件
const requiredFiles = [
  'extension.toml',
  'package.json',
  '.releaserc.json',
  'commitlint.config.js',
  'scripts/update-version.js',
  '.github/workflows/automated-release.yml',
  '.github/workflows/validate-commits.yml'
];

let allFilesExist = true;

requiredFiles.forEach(file => {
  const filePath = path.join(__dirname, '..', file);
  if (fs.existsSync(filePath)) {
    console.log(`✅ ${file}`);
  } else {
    console.log(`❌ ${file} - 文件不存在`);
    allFilesExist = false;
  }
});

// 检查 package.json 配置
try {
  const packageJson = JSON.parse(fs.readFileSync(path.join(__dirname, '..', 'package.json'), 'utf8'));
  
  console.log('\n📦 Package.json 配置:');
  console.log(`   版本: ${packageJson.version}`);
  console.log(`   名称: ${packageJson.name}`);
  
  if (packageJson.devDependencies && packageJson.devDependencies['semantic-release']) {
    console.log('✅ semantic-release 依赖已配置');
  } else {
    console.log('❌ semantic-release 依赖缺失');
    allFilesExist = false;
  }
} catch (error) {
  console.log('❌ package.json 解析失败:', error.message);
  allFilesExist = false;
}

// 检查 extension.toml 配置
try {
  const extensionToml = fs.readFileSync(path.join(__dirname, '..', 'extension.toml'), 'utf8');
  const versionMatch = extensionToml.match(/version = "(.+)"/);
  
  console.log('\n🎨 Extension.toml 配置:');
  if (versionMatch) {
    console.log(`   版本: ${versionMatch[1]}`);
    console.log('✅ 版本字段存在');
  } else {
    console.log('❌ 版本字段缺失');
    allFilesExist = false;
  }
} catch (error) {
  console.log('❌ extension.toml 读取失败:', error.message);
  allFilesExist = false;
}

console.log('\n' + '='.repeat(50));

if (allFilesExist) {
  console.log('🎉 所有配置文件验证通过！');
  console.log('\n📋 下一步操作:');
  console.log('1. 运行 npm install 安装依赖');
  console.log('2. 配置 GitHub Secrets (COMMITTER_TOKEN)');
  console.log('3. 创建 release 分支: git checkout -b release');
  console.log('4. 推送到 release 分支触发自动发布');
  process.exit(0);
} else {
  console.log('❌ 配置验证失败，请检查缺失的文件');
  process.exit(1);
}
