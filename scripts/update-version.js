#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// 获取新版本号
const newVersion = process.argv[2];
if (!newVersion) {
  console.error('Error: Version number is required');
  process.exit(1);
}

console.log(`Updating version to ${newVersion}`);

// 更新 extension.toml
const extensionTomlPath = path.join(__dirname, '..', 'extension.toml');
let extensionContent = fs.readFileSync(extensionTomlPath, 'utf8');

// 使用正则表达式替换版本号
extensionContent = extensionContent.replace(
  /^version = ".*"$/m,
  `version = "${newVersion}"`
);

fs.writeFileSync(extensionTomlPath, extensionContent);
console.log('Updated extension.toml');

// 更新 package.json 中的版本号
const packageJsonPath = path.join(__dirname, '..', 'package.json');
const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
packageJson.version = newVersion;
fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2) + '\n');
console.log('Updated package.json');

console.log('Version update completed successfully');
