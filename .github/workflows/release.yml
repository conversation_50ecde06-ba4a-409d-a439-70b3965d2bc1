on:
  push:
    branches:
      - release
  workflow_dispatch:
    inputs:
      version_type:
        description: 'Version bump type'
        required: true
        default: 'patch'
        type: choice
        options:
          - patch
          - minor
          - major

permissions:
  contents: write

jobs:
  auto-release:
    name: Auto Release Zed Extension
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Get current version
        id: current_version
        run: |
          VERSION=$(grep '^version = ' extension.toml | sed 's/version = "\(.*\)"/\1/')
          echo "current=$VERSION" >> $GITHUB_OUTPUT
          echo "Current version: $VERSION"

      - name: Determine version bump
        id: version_bump
        run: |
          if [ "${{ github.event_name }}" = "workflow_dispatch" ]; then
            BUMP_TYPE="${{ github.event.inputs.version_type }}"
          else
            # 自动检测提交信息中的版本类型
            COMMITS=$(git log --oneline $(git describe --tags --abbrev=0 2>/dev/null || echo "HEAD~10")..HEAD 2>/dev/null || git log --oneline -10)
            if echo "$COMMITS" | grep -i "breaking\|major"; then
              BUMP_TYPE="major"
            elif echo "$COMMITS" | grep -i "feat\|feature\|minor"; then
              BUMP_TYPE="minor"
            else
              BUMP_TYPE="patch"
            fi
          fi
          echo "type=$BUMP_TYPE" >> $GITHUB_OUTPUT
          echo "Version bump type: $BUMP_TYPE"

      - name: Calculate new version
        id: new_version
        run: |
          CURRENT="${{ steps.current_version.outputs.current }}"
          BUMP_TYPE="${{ steps.version_bump.outputs.type }}"

          IFS='.' read -r major minor patch <<< "$CURRENT"

          case $BUMP_TYPE in
            major)
              major=$((major + 1))
              minor=0
              patch=0
              ;;
            minor)
              minor=$((minor + 1))
              patch=0
              ;;
            patch)
              patch=$((patch + 1))
              ;;
          esac

          NEW_VERSION="$major.$minor.$patch"
          echo "version=$NEW_VERSION" >> $GITHUB_OUTPUT
          echo "New version: $NEW_VERSION"

      - name: Update extension.toml
        run: |
          sed -i 's/version = ".*"/version = "${{ steps.new_version.outputs.version }}"/' extension.toml
          echo "Updated extension.toml to version ${{ steps.new_version.outputs.version }}"

      - name: Commit version update
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"
          git add extension.toml
          git commit -m "chore: bump version to ${{ steps.new_version.outputs.version }}" || exit 0

      - name: Create and push tag
        run: |
          git tag "v${{ steps.new_version.outputs.version }}"
          git push origin "v${{ steps.new_version.outputs.version }}"

      - name: Push version update
        run: git push origin release

      - name: Publish to Zed Extensions
        uses: huacnlee/zed-extension-action@v1
        with:
          extension-name: 1984-theme
          push-to: chenmijiang/zed-extensions
          create-pullrequest: false
        env:
          COMMITTER_TOKEN: ${{ secrets.COMMITTER_TOKEN }}

      - name: Create GitHub Release
        uses: softprops/action-gh-release@v1
        with:
          tag_name: v${{ steps.new_version.outputs.version }}
          name: Release v${{ steps.new_version.outputs.version }}
          body: |
            ## Changes in v${{ steps.new_version.outputs.version }}

            Auto-generated release from release branch.

            View the full changelog and commit history for detailed changes.
          draft: false
          prerelease: false
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
