name: Validate Commits

on:
  pull_request:
    branches:
      - main
      - release
  push:
    branches:
      - main

jobs:
  validate:
    name: Validate Commit Messages
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'

      - name: Install commitlint
        run: |
          npm install -g @commitlint/cli @commitlint/config-conventional

      - name: Validate current commit (push)
        if: github.event_name == 'push'
        run: echo "${{ github.event.head_commit.message }}" | commitlint

      - name: Validate PR commits
        if: github.event_name == 'pull_request'
        run: commitlint --from ${{ github.event.pull_request.base.sha }} --to ${{ github.event.pull_request.head.sha }} --verbose
