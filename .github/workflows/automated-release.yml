name: Automated Release

on:
  push:
    branches:
      - release

permissions:
  contents: write
  issues: write
  pull-requests: write

jobs:
  release:
    name: Semantic Release
    runs-on: ubuntu-latest
    outputs:
      new-release-published: ${{ steps.semantic.outputs.new-release-published }}
      new-release-version: ${{ steps.semantic.outputs.new-release-version }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Make update script executable
        run: chmod +x scripts/update-version.js

      - name: Semantic Release
        id: semantic
        uses: cycjimmy/semantic-release-action@v4
        with:
          semantic_version: 22
          extra_plugins: |
            @semantic-release/changelog@6.0.3
            @semantic-release/git@10.0.1
            @semantic-release/exec@6.0.3
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

  publish-extension:
    name: Publish Zed Extension
    runs-on: ubuntu-latest
    needs: release
    if: needs.release.outputs.new-release-published == 'true'
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: release
          fetch-depth: 0

      - name: Pull latest changes
        run: git pull origin release

      - name: Publish to Zed Extensions
        uses: huacnlee/zed-extension-action@v1
        with:
          extension-name: 1984-theme
          push-to: chenmijiang/zed-extensions
          create-pullrequest: false
        env:
          COMMITTER_TOKEN: ${{ secrets.COMMITTER_TOKEN }}

      - name: Notify Success
        run: |
          echo "✅ Successfully published version ${{ needs.release.outputs.new-release-version }}"
          echo "📦 Extension published to chenmijiang/zed-extensions"
