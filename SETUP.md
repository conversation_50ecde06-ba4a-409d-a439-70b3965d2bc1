# 自动化发布流程设置指南

## 🚀 快速设置

### 1. 安装依赖

```bash
npm install
```

### 2. 验证配置

```bash
npm run validate
```

### 3. 配置 GitHub Secrets

在 GitHub 仓库设置中添加以下 secrets：

#### COMMITTER_TOKEN
1. 访问 [GitHub Personal Access Tokens](https://github.com/settings/tokens)
2. 点击 "Generate new token (classic)"
3. 设置权限：
   - `repo` (完整仓库访问权限)
   - `workflow` (工作流权限)
4. 复制生成的 token
5. 在仓库设置 → Secrets and variables → Actions 中添加 `COMMITTER_TOKEN`

### 4. 创建 release 分支

```bash
git checkout -b release
git push -u origin release
```

## 📋 使用流程

### 日常开发

```bash
# 在 main 分支开发
git checkout main

# 使用约定式提交
git add .
git commit -m "feat: add new theme variant"
git push origin main
```

### 发布新版本

```bash
# 合并到 release 分支
git checkout release
git merge main
git push origin release
```

推送到 `release` 分支后，GitHub Actions 将自动：
1. 分析提交历史确定版本号
2. 更新 `extension.toml` 和 `package.json` 版本
3. 生成 `CHANGELOG.md`
4. 创建 git tag
5. 发布到 `chenmijiang/zed-extensions`

## 🔧 约定式提交规范

| 类型 | 描述 | 版本影响 | 示例 |
|------|------|----------|------|
| `feat` | 新功能 | minor | `feat: add dark mode support` |
| `fix` | 修复 | patch | `fix: resolve color contrast issue` |
| `perf` | 性能优化 | patch | `perf: optimize theme loading` |
| `refactor` | 重构 | patch | `refactor: reorganize theme files` |
| `docs` | 文档 | 无 | `docs: update README` |
| `style` | 格式 | 无 | `style: fix indentation` |
| `test` | 测试 | 无 | `test: add theme validation tests` |
| `chore` | 构建/工具 | 无 | `chore: update dependencies` |

### 重大变更 (Major 版本)

```bash
# 方式1：使用感叹号
git commit -m "feat!: redesign theme structure"

# 方式2：在 footer 中说明
git commit -m "feat: redesign theme structure

BREAKING CHANGE: theme file structure has been completely redesigned"
```

## 📊 监控和维护

### 查看发布状态
- 访问 [Actions 页面](https://github.com/chenmijiang/zed-1984/actions)
- 查看 "Automated Release" 工作流状态

### 查看发布历史
- 访问 [Releases 页面](https://github.com/chenmijiang/zed-1984/releases)
- 查看自动生成的版本和变更日志

### 故障排除

#### 发布失败
1. 检查提交信息格式
2. 确认 `COMMITTER_TOKEN` 权限
3. 查看 Actions 日志

#### 版本号错误
1. 检查提交类型
2. 确认 BREAKING CHANGE 标记
3. 手动重新运行工作流

#### 回滚版本
```bash
git checkout release
git reset --hard <previous-commit>
git push --force-with-lease origin release
```

## 🛠️ 高级配置

### 自定义版本规则
编辑 `.releaserc.json` 中的 `releaseRules` 部分

### 自定义变更日志
编辑 `.releaserc.json` 中的 `presetConfig` 部分

### 添加发布前检查
在 `.github/workflows/automated-release.yml` 中添加额外的验证步骤

## 📞 支持

如有问题，请：
1. 查看 [RELEASE_GUIDE.md](./RELEASE_GUIDE.md) 详细文档
2. 检查 GitHub Actions 日志
3. 提交 Issue 到项目仓库
