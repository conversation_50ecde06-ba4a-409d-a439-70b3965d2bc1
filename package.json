{"name": "zed-1984-theme", "version": "0.0.1", "description": "1984 theme for <PERSON><PERSON>", "repository": {"type": "git", "url": "https://github.com/chenmijiang/zed-1984.git"}, "author": "chenmijiang <<EMAIL>>", "license": "MIT", "private": true, "scripts": {"validate": "node scripts/validate-config.js", "release:dry": "semantic-release --dry-run", "release": "semantic-release"}, "devDependencies": {"@semantic-release/changelog": "^6.0.3", "@semantic-release/git": "^10.0.1", "@semantic-release/github": "^9.2.6", "semantic-release": "^22.0.12"}, "release": {"branches": ["release"], "plugins": ["@semantic-release/commit-analyzer", "@semantic-release/release-notes-generator", ["@semantic-release/changelog", {"changelogFile": "CHANGELOG.md"}], ["@semantic-release/exec", {"prepareCmd": "node scripts/update-version.js ${nextRelease.version}"}], ["@semantic-release/git", {"assets": ["extension.toml", "CHANGELOG.md", "package.json"], "message": "chore(release): ${nextRelease.version} [skip ci]\n\n${nextRelease.notes}"}], "@semantic-release/github"]}}