# 1984 Theme for Zed IDE

[![Automated Release](https://github.com/chenmijiang/zed-1984/actions/workflows/automated-release.yml/badge.svg)](https://github.com/chenmijiang/zed-1984/actions/workflows/automated-release.yml)
[![Validate Commits](https://github.com/chenmijiang/zed-1984/actions/workflows/validate-commits.yml/badge.svg)](https://github.com/chenmijiang/zed-1984/actions/workflows/validate-commits.yml)

> A Zed theme extension based on juanmnl's VSCode theme [1984 Cyberpunk Color Theme](https://github.com/juanmnl/vs-1984). Special thanks to [juanmnl](https://github.com/juanmnl).

As a big fan of the Cyberpunk theme, I've adapted juanmnl's 1984 theme for Zed's UI. The primary modifications involved adjusting the color scheme to seamlessly integrate with Zed. I highly recommend using the Cyberpunk theme paired with JetBrains Mono or Fira Code fonts.

For more details, please refer to [juanmnl's 1984 project repository](https://github.com/juanmnl/vs-1984).

### Cyberpunk

![](https://raw.githubusercontent.com/chenmijiang/zed-1984/main/screenshots/cyberpunk.png)

### Orwellian

![](https://raw.githubusercontent.com/chenmijiang/zed-1984/main/screenshots/orwellian.png)

### 1984

**1984:** Regular + Bold font weights

![](https://raw.githubusercontent.com/chenmijiang/zed-1984/main/screenshots/1984.png)

**1984 Unbolded:** Only Regular font weight

![](https://raw.githubusercontent.com/chenmijiang/zed-1984/main/screenshots/1984-unbolded.png)

**1984 Fancy:** Regular + Italic font weights (recommended for long term usage)

![](https://raw.githubusercontent.com/chenmijiang/zed-1984/main/screenshots/1984-fancy.png)

**1984 Light:** Regular font weights (Works better w/o bracket pair colorizer)

![](https://raw.githubusercontent.com/chenmijiang/zed-1984/main/screenshots/1984-light.png)

## 🚀 自动化发布流程

本项目采用完全自动化的发布流程，基于语义版本管理和约定式提交规范。

### 快速开始

1. **开发**：在 `main` 分支进行开发，使用约定式提交格式
2. **发布**：合并到 `release` 分支并推送，自动触发发布流程
3. **完成**：系统自动更新版本、生成变更日志、发布到 Zed Extensions

### 约定式提交示例

```bash
feat: add new cyberpunk color variant    # 新功能 (minor版本)
fix: correct theme contrast issues       # 修复 (patch版本)
docs: update installation guide          # 文档 (不影响版本)
```

详细说明请参考 [RELEASE_GUIDE.md](./RELEASE_GUIDE.md)

## 📦 安装

通过 Zed 扩展市场安装，或手动安装：

1. 打开 Zed
2. 按 `Cmd+Shift+P` (macOS) 或 `Ctrl+Shift+P` (Linux/Windows)
3. 输入 "extensions" 并选择 "Extensions: Install Extensions"
4. 搜索 "1984 Theme" 并安装

## 🎨 主题变体
