# 自动化发布指南

## 🚀 发布方式

### 方式1：自动发布（推荐）

推送到 `release` 分支时自动触发发布：

```bash
# 合并更改到 release 分支
git checkout release
git merge main
git push origin release
```

系统会自动：
1. 检测提交信息确定版本类型
2. 更新 `extension.toml` 版本号
3. 创建 git tag
4. 发布到 `chenmijiang/zed-extensions`
5. 创建 GitHub Release

### 方式2：手动发布

在 GitHub Actions 页面手动触发，可选择版本类型：

1. 访问 [Actions 页面](https://github.com/chenmijiang/zed-1984/actions/workflows/release.yml)
2. 点击 "Run workflow"
3. 选择版本类型：
   - **patch** (0.0.X) - 修复bug
   - **minor** (0.X.0) - 新功能
   - **major** (X.0.0) - 重大变更

## 📋 版本自动检测规则

推送到 `release` 分支时，系统会分析最近的提交信息：

| 提交信息包含 | 版本类型 | 示例 |
|-------------|----------|------|
| `breaking`, `major` | major (1.0.0) | `fix: breaking change in theme structure` |
| `feat`, `feature`, `minor` | minor (0.1.0) | `feat: add new cyberpunk variant` |
| 其他 | patch (0.0.1) | `fix: color contrast issue` |

## 🔧 使用流程

### 日常开发
```bash
# 在 main 分支开发
git checkout main
git add .
git commit -m "feat: add new theme colors"
git push origin main
```

### 发布新版本
```bash
# 方法1：直接在 release 分支开发
git checkout release
git add .
git commit -m "feat: add dark mode support"
git push origin release

# 方法2：从 main 分支合并
git checkout release
git merge main
git push origin release
```

## 📊 当前状态

- **当前版本**: 0.0.1
- **发布分支**: release
- **目标仓库**: chenmijiang/zed-extensions

## 🛠️ 设置要求

确保 GitHub 仓库配置了以下 Secret：
- `COMMITTER_TOKEN`: 用于推送到目标仓库的个人访问令牌

## 📞 故障排除

### 发布失败
1. 检查 `COMMITTER_TOKEN` 权限
2. 确认 `extension.toml` 格式正确
3. 查看 GitHub Actions 日志

### 版本号错误
1. 检查提交信息是否包含正确的关键词
2. 使用手动发布指定版本类型
3. 手动编辑 `extension.toml` 后重新推送

### 回滚版本
```bash
git checkout release
git reset --hard <previous-commit>
git push --force-with-lease origin release
```
