{"branches": ["release"], "plugins": [["@semantic-release/commit-analyzer", {"preset": "conventionalcommits", "releaseRules": [{"type": "feat", "release": "minor"}, {"type": "fix", "release": "patch"}, {"type": "perf", "release": "patch"}, {"type": "revert", "release": "patch"}, {"type": "docs", "release": false}, {"type": "style", "release": false}, {"type": "chore", "release": false}, {"type": "refactor", "release": "patch"}, {"type": "test", "release": false}, {"type": "build", "release": false}, {"type": "ci", "release": false}, {"scope": "no-release", "release": false}]}], ["@semantic-release/release-notes-generator", {"preset": "conventionalcommits", "presetConfig": {"types": [{"type": "feat", "section": "✨ Features"}, {"type": "fix", "section": "🐛 Bug Fixes"}, {"type": "perf", "section": "⚡ Performance Improvements"}, {"type": "revert", "section": "⏪ Reverts"}, {"type": "refactor", "section": "♻️ Code Refactoring"}]}}], ["@semantic-release/changelog", {"changelogFile": "CHANGELOG.md"}], ["@semantic-release/exec", {"prepareCmd": "node scripts/update-version.js ${nextRelease.version}"}], ["@semantic-release/git", {"assets": ["extension.toml", "CHANGELOG.md", "package.json"], "message": "chore(release): ${nextRelease.version} [skip ci]\n\n${nextRelease.notes}"}], "@semantic-release/github"]}